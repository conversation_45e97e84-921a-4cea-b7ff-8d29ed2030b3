<!DOCTYPE html>
<html>
<head>
    <title>Gulf Regional Cables: Qatar-Bahrain-UAE Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .cable-info { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .critical { background-color: #fff3cd; border-color: #ffeaa7; }
        .regional { background-color: #d1ecf1; border-color: #bee5eb; }
        .international { background-color: #d4edda; border-color: #c3e6cb; }
        .summary { background-color: #f8f9fa; border-color: #dee2e6; }
        .coordinates { font-family: monospace; font-size: 11px; margin: 5px 0; color: #666; }
        .properties { margin: 10px 0; }
        .properties strong { color: #495057; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .recommendation { background-color: #e7f3ff; border-left: 4px solid #007bff; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Gulf Regional Cables Analysis: Qatar ↔ Bahrain ↔ UAE</h1>
    
    <div class="cable-info summary">
        <h3>Analysis Objectives</h3>
        <p><strong>Purpose:</strong> Evaluate Qatar-Bahrain-UAE submarine cable connections for regional telecommunications importance</p>
        <ul>
            <li><strong>Identify:</strong> All cables connecting these three Gulf countries</li>
            <li><strong>Assess:</strong> Regional vs. international connectivity patterns</li>
            <li><strong>Classify:</strong> Critical regional infrastructure vs. broader international systems</li>
            <li><strong>Recommend:</strong> Selective filtering strategy to preserve important Gulf connections</li>
        </ul>
    </div>

    <div id="analysis-results"></div>

    <script>
        // Gulf countries for analysis
        const gulfCountries = new Set(['Qatar', 'Bahrain', 'United Arab Emirates', 'UAE']);
        
        // Known Gulf regional cables
        const knownGulfCables = [
            'qatar-u-a-e-submarine-cable-system',
            'fiber-optic-gulf-fog',
            'gulf-bridge-international-cable-system-gbicsmiddle-east-north-africa-mena-cable-system',
            'tata-tgn-gulf'
        ];

        // Function to analyze cable connectivity
        function analyzeCableConnectivity(cable) {
            const landingPoints = cable.properties.landing_points || [];
            const countries = landingPoints.map(lp => lp.country).filter(Boolean);
            const uniqueCountries = [...new Set(countries)];
            
            const gulfCountriesInCable = uniqueCountries.filter(country => 
                gulfCountries.has(country) || country.toLowerCase().includes('emirates')
            );
            
            const nonGulfCountries = uniqueCountries.filter(country => 
                !gulfCountries.has(country) && !country.toLowerCase().includes('emirates')
            );

            return {
                allCountries: uniqueCountries,
                gulfCountries: gulfCountriesInCable,
                nonGulfCountries: nonGulfCountries,
                isGulfRegional: gulfCountriesInCable.length >= 2 && nonGulfCountries.length === 0,
                isGulfInternational: gulfCountriesInCable.length >= 1 && nonGulfCountries.length > 0,
                connectsQatarBahrainUAE: gulfCountriesInCable.length >= 2
            };
        }

        // Function to classify cable importance
        function classifyCableImportance(cable, connectivity) {
            if (connectivity.isGulfRegional && connectivity.connectsQatarBahrainUAE) {
                return {
                    category: 'critical',
                    importance: 'Critical Regional Infrastructure',
                    description: 'Essential for Gulf regional telecommunications connectivity'
                };
            } else if (connectivity.isGulfInternational && connectivity.connectsQatarBahrainUAE) {
                return {
                    category: 'international',
                    importance: 'International Gateway',
                    description: 'Provides Gulf regional access to global networks'
                };
            } else if (connectivity.gulfCountries.length > 0) {
                return {
                    category: 'regional',
                    importance: 'Regional Connection',
                    description: 'Connects Gulf countries to broader regional networks'
                };
            } else {
                return {
                    category: 'other',
                    importance: 'Other',
                    description: 'Not directly relevant to Gulf regional connectivity'
                };
            }
        }

        // Load and analyze cable data
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('analysis-results');
                
                // Find cables with Gulf country connections
                const gulfRelatedCables = data.features.filter(feature => {
                    const props = feature.properties;
                    
                    // Check if cable name contains Gulf-related terms
                    const nameMatch = props.name && (
                        props.name.toLowerCase().includes('gulf') ||
                        props.name.toLowerCase().includes('qatar') ||
                        props.name.toLowerCase().includes('bahrain') ||
                        props.name.toLowerCase().includes('uae') ||
                        props.name.toLowerCase().includes('emirates')
                    );
                    
                    // Check if cable ID is in known Gulf cables
                    const idMatch = knownGulfCables.includes(props.id);
                    
                    // Check landing points for Gulf countries
                    const landingPointMatch = props.landing_points && 
                        props.landing_points.some(lp => 
                            lp.country && (
                                gulfCountries.has(lp.country) || 
                                lp.country.toLowerCase().includes('emirates')
                            )
                        );
                    
                    return nameMatch || idMatch || landingPointMatch;
                });

                console.log(`Found ${gulfRelatedCables.length} Gulf-related cables`);

                // Analyze each cable
                const analysisResults = gulfRelatedCables.map(cable => {
                    const connectivity = analyzeCableConnectivity(cable);
                    const importance = classifyCableImportance(cable, connectivity);
                    
                    return {
                        cable,
                        connectivity,
                        importance
                    };
                });

                // Sort by importance
                analysisResults.sort((a, b) => {
                    const order = { 'critical': 0, 'international': 1, 'regional': 2, 'other': 3 };
                    return order[a.importance.category] - order[b.importance.category];
                });

                // Display results
                analysisResults.forEach(result => {
                    const { cable, connectivity, importance } = result;
                    const props = cable.properties;
                    
                    const div = document.createElement('div');
                    div.className = `cable-info ${importance.category}`;
                    
                    // Create landing points table
                    let landingPointsTable = '';
                    if (props.landing_points && props.landing_points.length > 0) {
                        landingPointsTable = `
                            <table>
                                <tr><th>Landing Point</th><th>Country</th></tr>
                                ${props.landing_points.map(lp => 
                                    `<tr><td>${lp.name || 'N/A'}</td><td>${lp.country || 'N/A'}</td></tr>`
                                ).join('')}
                            </table>
                        `;
                    }
                    
                    div.innerHTML = `
                        <h3>${props.name}</h3>
                        <div class="properties">
                            <strong>Classification:</strong> ${importance.importance}<br>
                            <strong>Description:</strong> ${importance.description}<br>
                            <strong>Length:</strong> ${props.length || 'N/A'}<br>
                            <strong>Owners:</strong> ${props.owners || 'N/A'}<br>
                            <strong>Ready for Service:</strong> ${props.rfs || 'N/A'}<br>
                            <strong>Suppliers:</strong> ${props.suppliers || 'N/A'}<br>
                            <strong>URL:</strong> ${props.url ? `<a href="${props.url}" target="_blank">Link</a>` : 'N/A'}
                        </div>
                        <div class="properties">
                            <strong>Connectivity Analysis:</strong><br>
                            • Gulf Countries Connected: ${connectivity.gulfCountries.join(', ') || 'None'}<br>
                            • Other Countries: ${connectivity.nonGulfCountries.slice(0, 5).join(', ')}${connectivity.nonGulfCountries.length > 5 ? '...' : ''}<br>
                            • Total Countries: ${connectivity.allCountries.length}<br>
                            • Regional Gulf Cable: ${connectivity.isGulfRegional ? 'Yes' : 'No'}<br>
                            • International Gateway: ${connectivity.isGulfInternational ? 'Yes' : 'No'}
                        </div>
                        ${landingPointsTable}
                    `;
                    resultsDiv.appendChild(div);
                });

                // Create summary and recommendations
                const criticalCables = analysisResults.filter(r => r.importance.category === 'critical');
                const internationalCables = analysisResults.filter(r => r.importance.category === 'international');
                const regionalCables = analysisResults.filter(r => r.importance.category === 'regional');

                const summaryDiv = document.createElement('div');
                summaryDiv.className = 'cable-info summary';
                summaryDiv.innerHTML = `
                    <h3>Analysis Summary</h3>
                    <table>
                        <tr><th>Category</th><th>Count</th><th>Cables</th></tr>
                        <tr><td>Critical Regional Infrastructure</td><td>${criticalCables.length}</td><td>${criticalCables.map(r => r.cable.properties.name).join(', ')}</td></tr>
                        <tr><td>International Gateways</td><td>${internationalCables.length}</td><td>${internationalCables.map(r => r.cable.properties.name).join(', ')}</td></tr>
                        <tr><td>Regional Connections</td><td>${regionalCables.length}</td><td>${regionalCables.map(r => r.cable.properties.name).join(', ')}</td></tr>
                    </table>
                `;
                resultsDiv.appendChild(summaryDiv);

                // Add recommendations
                const recommendationDiv = document.createElement('div');
                recommendationDiv.className = 'recommendation';
                recommendationDiv.innerHTML = `
                    <h3>🔧 Filtering Recommendations</h3>
                    <p><strong>Current Status:</strong> Middle East filtering has been simplified in the current implementation.</p>
                    
                    <h4>Recommended Strategy:</h4>
                    <ol>
                        <li><strong>Preserve Critical Regional Infrastructure:</strong> Keep Qatar-Bahrain-UAE interconnection cables visible</li>
                        <li><strong>Maintain International Gateways:</strong> Keep cables that provide Gulf access to global networks</li>
                        <li><strong>Selective Regional Filtering:</strong> Consider filtering purely regional cables that don't serve Qatar-Bahrain-UAE connectivity</li>
                    </ol>
                    
                    <h4>Implementation Approach:</h4>
                    <ul>
                        <li>Create a "Gulf Regional Exception" list for critical cables</li>
                        <li>Implement cable ID-based filtering for known important systems</li>
                        <li>Preserve existing Americas and Asia-Pacific filtering</li>
                        <li>Add console logging for transparency</li>
                    </ul>
                `;
                resultsDiv.appendChild(recommendationDiv);

                // Log detailed analysis
                console.log('=== GULF CABLES DETAILED ANALYSIS ===');
                analysisResults.forEach(result => {
                    console.log(`\n${result.cable.properties.name}:`);
                    console.log(`  Category: ${result.importance.importance}`);
                    console.log(`  Gulf Countries: ${result.connectivity.gulfCountries.join(', ')}`);
                    console.log(`  Other Countries: ${result.connectivity.nonGulfCountries.join(', ')}`);
                    console.log(`  Regional: ${result.connectivity.isGulfRegional}`);
                    console.log(`  International: ${result.connectivity.isGulfInternational}`);
                });
            })
            .catch(error => {
                console.error('Error loading cable data:', error);
                document.getElementById('analysis-results').innerHTML = `
                    <div class="cable-info summary">
                        <h3>Error</h3>
                        <p>Failed to load cable data: ${error.message}</p>
                    </div>
                `;
            });
    </script>
</body>
</html>
