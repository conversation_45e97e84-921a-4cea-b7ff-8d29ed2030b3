# Americas Region Cable Filter

## Overview
This modification filters out (hides) submarine cables and landing points located in the Americas region from the cable map visualization. The filtering affects both the cable routes and landing points, so cables connecting to or passing through North America, Central America, and South America are not displayed on the map.

## What's Changed

### Files Modified
- `html/index.html` - Main visualization file with filtering logic added
- `html/test-filter.html` - Test page to verify filtering functionality (newly created)

### Filtering Logic
The filtering works by checking the geographic coordinates of each cable and landing point:

1. **Cable Filtering**: Examines the MultiLineString coordinates of each cable route
2. **Landing Point Filtering**: Checks the Point coordinates of each landing point
3. **Geographic Range**: Uses longitude range of -180° to -25° to identify Americas region
4. **Recursive Coordinate Checking**: Handles nested coordinate arrays in GeoJSON format

### Americas Region Definition
The filter identifies cables and landing points in the Americas based on longitude coordinates:
- **Longitude Range**: -180° to -25° (covers Alaska to eastern Brazil)
- **Includes**: 
  - North America (USA, Canada, Mexico, Greenland)
  - Central America and Caribbean (all countries and territories)
  - South America (all countries including Brazil, Argentina, Chile, etc.)

### Visual Indicators
- Warning message in the info panel: "⚠️ Americas region cables are hidden"
- Console logging shows which cables and landing points are being filtered
- Test page available to verify filtering logic

## How It Works

### Cable Filtering Process
1. Loads cable data from `cable-geo.json`
2. For each cable feature, recursively checks all coordinate points
3. If any coordinate point falls within Americas longitude range, the entire cable is filtered out
4. Remaining cables are displayed normally

### Landing Point Filtering Process
1. Loads landing point data from `landing-point-geo.json`
2. For each landing point, checks its coordinate pair
3. If longitude falls within Americas range, the landing point is filtered out
4. Remaining landing points are displayed normally

### Coordinate Structure Handling
The filtering function handles the complex nested coordinate structures in GeoJSON:
- MultiLineString coordinates: `[[[lng, lat], [lng, lat], ...], [[lng, lat], ...]]`
- Point coordinates: `[lng, lat]`
- Recursive checking ensures all coordinate levels are examined

## Testing
Use the test page `html/test-filter.html` to verify the filtering logic:
- Tests coordinate detection with sample data
- Shows actual cable filtering results
- Provides console output for debugging

## Usage
Simply open `html/index.html` in a web browser. The Americas region cables will be automatically filtered out, and you'll see:
- Only cables outside the Americas region
- Only landing points outside the Americas region
- A warning message indicating the filter is active
- Console logs showing which items were filtered (open browser dev tools to see)

## Technical Details
- **Longitude Range**: -180° to -25° (chosen to be inclusive of all Americas territories)
- **Filtering Method**: Client-side JavaScript filtering of GeoJSON data
- **Performance**: Filtering happens once when data loads, minimal impact on map performance
- **Compatibility**: Works with existing Leaflet.js map implementation

## Reverting Changes
To restore the original functionality (show all cables), simply replace the modified `html/index.html` with the original version, or remove the filtering logic from the fetch operations.
