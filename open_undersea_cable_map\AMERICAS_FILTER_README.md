# Americas and Asia-Pacific Region Cable Filter

## Overview
This modification filters out (hides) submarine cables and landing points located in both the Americas and Asia-Pacific regions from the cable map visualization. The filtering affects both the cable routes and landing points, so cables connecting to or passing through the Americas (North America, Central America, and South America) and Asia-Pacific (East Asia, Southeast Asia, South Asia, and Oceania) are not displayed on the map.

## What's Changed

### Files Modified
- `html/index.html` - Main visualization file with filtering logic added
- `html/test-filter.html` - Test page to verify filtering functionality (newly created)

### Filtering Logic
The filtering works by checking the geographic coordinates of each cable and landing point against both regional filters:

1. **Cable Filtering**: Examines the MultiLineString coordinates of each cable route
2. **Landing Point Filtering**: Checks the Point coordinates of each landing point
3. **Dual Regional Filtering**: Applies both Americas and Asia-Pacific coordinate checks
4. **Recursive Coordinate Checking**: Handles nested coordinate arrays in GeoJSON format

### Americas Region Definition
The filter identifies cables and landing points in the Americas based on longitude coordinates:
- **Longitude Range**: -180° to -25° (covers Alaska to eastern Brazil)
- **Includes**:
  - North America (USA, Canada, Mexico, Greenland)
  - Central America and Caribbean (all countries and territories)
  - South America (all countries including Brazil, Argentina, Chile, etc.)

### Asia-Pacific Region Definition (Adjusted to Preserve African Infrastructure)
The filter identifies cables and landing points in the Asia-Pacific based on coordinate ranges:
- **Main Asia-Pacific**: Longitude 65°E to 180°E, Latitude -50°S to 80°N (adjusted from 60°E to exclude East Africa)
- **Pacific Extension**: Longitude -180°E to -120°W, Latitude -50°S to 80°N (for Pacific islands)
- **Includes**:
  - East Asia (China, Japan, South Korea, North Korea, Taiwan, Hong Kong, Macau, Mongolia)
  - Southeast Asia (Singapore, Indonesia, Philippines, Malaysia, Vietnam, Thailand, Myanmar, Cambodia, Laos, Brunei, Timor-Leste)
  - South Asia (India, Pakistan, Bangladesh, Sri Lanka, Nepal, Bhutan, Maldives, Afghanistan)
  - Oceania (Australia, New Zealand, Papua New Guinea, Fiji, Solomon Islands, Vanuatu, New Caledonia, Samoa, Tonga, and other Pacific island nations)
- **Excludes**: East Africa and Middle East (preserved for African infrastructure visibility)

### Visual Indicators
- Warning message in the info panel: "⚠️ Americas and Asia-Pacific region cables are hidden"
- Console logging shows which cables and landing points are being filtered from both regions
- Test page available to verify both filtering systems

## How It Works

### Cable Filtering Process
1. Loads cable data from `cable-geo.json`
2. For each cable feature, recursively checks all coordinate points
3. If any coordinate point falls within Americas longitude range, the entire cable is filtered out
4. Remaining cables are displayed normally

### Landing Point Filtering Process
1. Loads landing point data from `landing-point-geo.json`
2. For each landing point, checks its coordinate pair
3. If longitude falls within Americas range, the landing point is filtered out
4. Remaining landing points are displayed normally

### Coordinate Structure Handling
The filtering function handles the complex nested coordinate structures in GeoJSON:
- MultiLineString coordinates: `[[[lng, lat], [lng, lat], ...], [[lng, lat], ...]]`
- Point coordinates: `[lng, lat]`
- Recursive checking ensures all coordinate levels are examined

## African Infrastructure Preservation
The filtering system has been specifically designed to preserve important African submarine cable infrastructure:

### 2Africa Cable System
- **Major submarine cable**: Connects Africa to Europe, Middle East, and Asia (45,000 km, 2023)
- **South African Landing Points**: Cape Town, Mtunzini, Port Elizabeth/Gqeberha
- **East African Connections**: Kenya (Mombasa), Tanzania (Dar Es Salaam), Somalia, Djibouti
- **West African Connections**: Nigeria, Ghana, Côte d'Ivoire, Senegal, and others

### Other Major African Systems
- **WACS** (West Africa Cable System)
- **ACE** (Africa Coast to Europe)
- **EASSY** (Eastern Africa Submarine System)
- **AAE-1** (Asia Africa Europe-1)
- **SAFE** (South Africa Far East)
- **SAT-3/WASC** (South Atlantic 3/West Africa Submarine Cable)

### Technical Implementation
- **Geographic Boundary Adjustment**: Asia-Pacific filter adjusted from 60°E to 65°E to exclude East Africa
- **Result**: All African submarine cables and landing points remain visible on the map
- **Preserved Regions**: West Africa, East Africa, Southern Africa, North Africa

## Testing
Use the test pages to verify the filtering logic:
- `html/test-filter.html` - Americas and Asia-Pacific filtering tests
- `html/test-african-cables.html` - African cable preservation verification
- Tests coordinate detection with sample data
- Shows actual cable filtering results
- Provides console output for debugging

## Usage
Simply open `html/index.html` in a web browser. The Americas region cables will be automatically filtered out, and you'll see:
- Only cables outside the Americas region
- Only landing points outside the Americas region
- A warning message indicating the filter is active
- Console logs showing which items were filtered (open browser dev tools to see)

## Technical Details
- **Longitude Range**: -180° to -25° (chosen to be inclusive of all Americas territories)
- **Filtering Method**: Client-side JavaScript filtering of GeoJSON data
- **Performance**: Filtering happens once when data loads, minimal impact on map performance
- **Compatibility**: Works with existing Leaflet.js map implementation

## Reverting Changes
To restore the original functionality (show all cables), simply replace the modified `html/index.html` with the original version, or remove the filtering logic from the fetch operations.
