# Multi-Regional Cable Filter (Americas, Asia-Pacific, and Selective Middle East)

## Overview
This modification implements a sophisticated three-layer filtering system for submarine cables and landing points:
1. **Americas Region**: Complete filtering of all cables in North America, Central America, and South America
2. **Asia-Pacific Region**: Complete filtering of all cables in East Asia, Southeast Asia, South Asia, and Oceania
3. **Middle East Region**: Selective connectivity-based filtering that hides isolated Middle East cables while preserving those with Africa/Europe connections

The filtering affects both cable routes and landing points, creating a focused view of global submarine cable infrastructure.

## What's Changed

### Files Modified
- `html/index.html` - Main visualization file with filtering logic added
- `html/test-filter.html` - Test page to verify filtering functionality (newly created)

### Filtering Logic
The filtering works by checking the geographic coordinates of each cable and landing point against both regional filters:

1. **Cable Filtering**: Examines the MultiLineString coordinates of each cable route
2. **Landing Point Filtering**: Checks the Point coordinates of each landing point
3. **Dual Regional Filtering**: Applies both Americas and Asia-Pacific coordinate checks
4. **Recursive Coordinate Checking**: Handles nested coordinate arrays in GeoJSON format

### Americas Region Definition
The filter identifies cables and landing points in the Americas based on longitude coordinates:
- **Longitude Range**: -180° to -25° (covers Alaska to eastern Brazil)
- **Includes**:
  - North America (USA, Canada, Mexico, Greenland)
  - Central America and Caribbean (all countries and territories)
  - South America (all countries including Brazil, Argentina, Chile, etc.)

### Asia-Pacific Region Definition
The filter identifies cables and landing points in the Asia-Pacific based on coordinate ranges:
- **Main Asia-Pacific**: Longitude 60°E to 180°E, Latitude -50°S to 80°N
- **Pacific Extension**: Longitude -180°E to -120°W, Latitude -50°S to 80°N (for Pacific islands)
- **Includes**:
  - East Asia (China, Japan, South Korea, North Korea, Taiwan, Hong Kong, Macau, Mongolia)
  - Southeast Asia (Singapore, Indonesia, Philippines, Malaysia, Vietnam, Thailand, Myanmar, Cambodia, Laos, Brunei, Timor-Leste)
  - South Asia (India, Pakistan, Bangladesh, Sri Lanka, Nepal, Bhutan, Maldives, Afghanistan)
  - Oceania (Australia, New Zealand, Papua New Guinea, Fiji, Solomon Islands, Vanuatu, New Caledonia, Samoa, Tonga, and other Pacific island nations)

### Middle East Region Definition (Selective Connectivity-Based Filtering)
The filter applies intelligent connectivity analysis for Middle East cables:
- **Geographic Range**: Longitude 25°E to 65°E, Latitude 10°N to 45°N
- **Countries Included**: Saudi Arabia, UAE, Qatar, Kuwait, Bahrain, Oman, Yemen, Iran, Iraq, Israel, Palestine, Jordan, Lebanon, Syria, Turkey, Cyprus
- **Filtering Logic**:
  - **HIDE**: Cables entirely within Middle East region
  - **HIDE**: Cables connecting Middle East only to Americas or Asia-Pacific (already filtered regions)
  - **KEEP**: Cables connecting Middle East to Africa or Europe (even if they also connect to other regions)
- **Supporting Regions for Connectivity Analysis**:
  - **Africa**: Longitude -20°W to 55°E, Latitude -35°S to 40°N
  - **Europe**: Longitude -25°W to 45°E, Latitude 35°N to 75°N

### Visual Indicators
- Warning message in the info panel: "⚠️ Americas, Asia-Pacific, and isolated Middle East cables are hidden"
- Console logging shows which cables and landing points are being filtered from all three regional filters
- Test pages available to verify all filtering systems:
  - `test-filter.html` - Americas and Asia-Pacific filtering tests
  - `test-middle-east-filter.html` - Middle East connectivity-based filtering tests

## How It Works

### Cable Filtering Process
1. Loads cable data from `cable-geo.json`
2. For each cable feature, recursively checks all coordinate points
3. If any coordinate point falls within Americas longitude range, the entire cable is filtered out
4. Remaining cables are displayed normally

### Landing Point Filtering Process
1. Loads landing point data from `landing-point-geo.json`
2. For each landing point, checks its coordinate pair
3. If longitude falls within Americas range, the landing point is filtered out
4. Remaining landing points are displayed normally

### Coordinate Structure Handling
The filtering function handles the complex nested coordinate structures in GeoJSON:
- MultiLineString coordinates: `[[[lng, lat], [lng, lat], ...], [[lng, lat], ...]]`
- Point coordinates: `[lng, lat]`
- Recursive checking ensures all coordinate levels are examined

## Testing
Use the test page `html/test-filter.html` to verify the filtering logic:
- Tests coordinate detection with sample data
- Shows actual cable filtering results
- Provides console output for debugging

## Usage
Simply open `html/index.html` in a web browser. The Americas region cables will be automatically filtered out, and you'll see:
- Only cables outside the Americas region
- Only landing points outside the Americas region
- A warning message indicating the filter is active
- Console logs showing which items were filtered (open browser dev tools to see)

## Technical Details
- **Longitude Range**: -180° to -25° (chosen to be inclusive of all Americas territories)
- **Filtering Method**: Client-side JavaScript filtering of GeoJSON data
- **Performance**: Filtering happens once when data loads, minimal impact on map performance
- **Compatibility**: Works with existing Leaflet.js map implementation

## Reverting Changes
To restore the original functionality (show all cables), simply replace the modified `html/index.html` with the original version, or remove the filtering logic from the fetch operations.
