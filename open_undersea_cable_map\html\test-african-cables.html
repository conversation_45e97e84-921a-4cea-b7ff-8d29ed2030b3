<!DOCTYPE html>
<html>
<head>
    <title>African Submarine Cables Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .cable-info { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .visible { background-color: #d4edda; border-color: #c3e6cb; }
        .filtered { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .cable-details { font-family: monospace; font-size: 12px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .coordinates { color: #666; font-size: 11px; }
    </style>
</head>
<body>
    <h1>African Submarine Cables Analysis</h1>
    
    <div class="cable-info info">
        <h3>🔍 Investigation: Missing 2Africa Cable and African Infrastructure</h3>
        <p><strong>Purpose:</strong> Analyze why African submarine cables, particularly the 2Africa system, may not be appearing on the map</p>
        
        <h4>Key Questions:</h4>
        <ul>
            <li>Is the 2Africa cable being filtered by current geographic filtering logic?</li>
            <li>Are African landing points being incorrectly filtered?</li>
            <li>Do geographic boundaries exclude African coordinates?</li>
            <li>Are there data availability issues with African cables?</li>
        </ul>
        
        <h4>Expected African Cables to Analyze:</h4>
        <ul>
            <li><strong>2Africa</strong> - Major system connecting Africa to Europe, Middle East, and Asia</li>
            <li><strong>West Africa Cable System (WACS)</strong> - West African coastal system</li>
            <li><strong>Africa Coast to Europe (ACE)</strong> - West Africa to Europe</li>
            <li><strong>South African cables</strong> - SAFE, SAT-3/WASC, Equiano</li>
        </ul>
    </div>

    <div id="analysis-results"></div>

    <script>
        // Copy filtering functions from main implementation
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    
                    // Americas region boundaries:
                    // Longitude: -180°W to -30°W, Latitude: -60°S to 85°N
                    return (lng >= -180 && lng <= -30) && (lat >= -60 && lat <= 85);
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    
                    // Asia-Pacific region boundaries (UPDATED - adjusted to exclude East Africa):
                    // Main Asia-Pacific: Longitude 65°E to 180°E, Latitude -50°S to 80°N
                    // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);
                    
                    return inMainAsiaPacific || inPacificExtension;
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        function isInMiddleEastRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    
                    // Middle East region boundaries:
                    // Longitude: 25°E to 65°E, Latitude: 10°N to 45°N
                    return (lng >= 25 && lng <= 65) && (lat >= 10 && lat <= 45);
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Define Africa region for analysis
        function isInAfricaRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    
                    // Africa region boundaries:
                    // Longitude: -20°W to 55°E, Latitude: -35°S to 40°N
                    return (lng >= -20 && lng <= 55) && (lat >= -35 && lat <= 40);
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Gulf regional cables exception
        const criticalGulfRegionalCables = new Set([
            'qatar-u-a-e-submarine-cable-system',
            'fiber-optic-gulf-fog',
            'gulf-bridge-international-cable-system-gbicsmiddle-east-north-africa-mena-cable-system',
            'tata-tgn-gulf'
        ]);

        function isCriticalGulfRegionalCable(cableId) {
            return criticalGulfRegionalCables.has(cableId);
        }

        function shouldFilterMiddleEastCable(cableId, coordinates) {
            if (isCriticalGulfRegionalCable(cableId)) {
                return false; // Don't filter - these are critical regional infrastructure
            }

            const hasMiddleEast = isInMiddleEastRegion(coordinates);
            if (!hasMiddleEast) return false;

            return false; // For now, keep other Middle East cables visible
        }

        // Known African cables to analyze
        const knownAfricanCables = [
            '2africa',
            'west-africa-cable-system-wacs',
            'africa-coast-to-europe-ace',
            'safe',
            'sat-3wasc',
            'equiano',
            'meltingpot-indianoceanic-submarine-system-metiss'
        ];

        // Load and analyze cable data
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('analysis-results');
                
                // Find African-related cables
                const africanCables = data.features.filter(feature => {
                    const props = feature.properties;
                    
                    // Check if cable name contains African terms
                    const nameMatch = props.name && (
                        props.name.toLowerCase().includes('africa') ||
                        props.name.toLowerCase().includes('wacs') ||
                        props.name.toLowerCase().includes('ace') ||
                        props.name.toLowerCase().includes('safe') ||
                        props.name.toLowerCase().includes('equiano') ||
                        props.name.toLowerCase().includes('metiss')
                    );
                    
                    // Check if cable ID is in known African cables
                    const idMatch = knownAfricanCables.includes(props.id);
                    
                    // Check if cable has African coordinates
                    const coords = feature.geometry ? feature.geometry.coordinates : null;
                    const hasAfricanCoords = coords ? isInAfricaRegion(coords) : false;
                    
                    return nameMatch || idMatch || hasAfricanCoords;
                });

                console.log(`Found ${africanCables.length} African-related cables`);

                // Analyze each cable
                const analysisResults = africanCables.map(cable => {
                    const coords = cable.geometry ? cable.geometry.coordinates : null;
                    const cableId = cable.properties.id;
                    const cableName = cable.properties.name;
                    
                    // Apply current filtering logic
                    const isAmericas = coords ? isInAmericasRegion(coords) : false;
                    const isAsiaPacific = coords ? isInAsiaPacificRegion(coords) : false;
                    const shouldFilterMiddleEast = coords ? shouldFilterMiddleEastCable(cableId, coords) : false;
                    const isGulfRegional = isCriticalGulfRegionalCable(cableId);
                    
                    // Determine if cable would be filtered
                    let wouldBeFiltered = false;
                    let filterReason = '';
                    
                    if (isGulfRegional) {
                        wouldBeFiltered = false;
                        filterReason = 'Preserved (Gulf Regional Exception)';
                    } else if (isAmericas) {
                        wouldBeFiltered = true;
                        filterReason = 'Filtered (Americas Region)';
                    } else if (isAsiaPacific) {
                        wouldBeFiltered = true;
                        filterReason = 'Filtered (Asia-Pacific Region)';
                    } else if (shouldFilterMiddleEast) {
                        wouldBeFiltered = true;
                        filterReason = 'Filtered (Middle East Region)';
                    } else {
                        wouldBeFiltered = false;
                        filterReason = 'Visible (Not Filtered)';
                    }
                    
                    // Regional analysis
                    const hasAfrica = coords ? isInAfricaRegion(coords) : false;
                    const hasMiddleEast = coords ? isInMiddleEastRegion(coords) : false;
                    const hasEurope = coords ? isInEuropeRegion(coords) : false;
                    
                    return {
                        cable,
                        coords,
                        wouldBeFiltered,
                        filterReason,
                        isAmericas,
                        isAsiaPacific,
                        hasAfrica,
                        hasMiddleEast,
                        hasEurope,
                        isGulfRegional
                    };
                });

                // Sort by filter status and importance
                analysisResults.sort((a, b) => {
                    if (a.wouldBeFiltered !== b.wouldBeFiltered) {
                        return a.wouldBeFiltered ? 1 : -1; // Visible first
                    }
                    return a.cable.properties.name.localeCompare(b.cable.properties.name);
                });

                // Display results
                analysisResults.forEach(result => {
                    const { cable, coords, wouldBeFiltered, filterReason } = result;
                    const props = cable.properties;
                    
                    const div = document.createElement('div');
                    div.className = `cable-info ${wouldBeFiltered ? 'filtered' : 'visible'}`;
                    
                    // Create connectivity summary
                    let connectivity = [];
                    if (result.hasAfrica) connectivity.push('Africa');
                    if (result.hasEurope) connectivity.push('Europe');
                    if (result.hasMiddleEast) connectivity.push('Middle East');
                    if (result.isAmericas) connectivity.push('Americas');
                    if (result.isAsiaPacific) connectivity.push('Asia-Pacific');
                    
                    div.innerHTML = `
                        <h3>${wouldBeFiltered ? '❌ FILTERED' : '✅ VISIBLE'}: ${props.name}</h3>
                        <div class="cable-details">
                            <strong>Cable ID:</strong> ${props.id}<br>
                            <strong>Length:</strong> ${props.length || 'N/A'}<br>
                            <strong>Owners:</strong> ${props.owners || 'N/A'}<br>
                            <strong>Ready for Service:</strong> ${props.rfs || 'N/A'}<br>
                            <strong>Status:</strong> ${filterReason}<br>
                            <strong>Regional Connectivity:</strong> ${connectivity.join(', ') || 'Unknown'}<br>
                            <strong>Has Coordinates:</strong> ${coords ? 'Yes' : 'No'}<br>
                            ${coords ? `<div class="coordinates">Sample coordinates: ${JSON.stringify(coords[0]?.slice(0, 2))}</div>` : ''}
                        </div>
                    `;
                    resultsDiv.appendChild(div);
                });

                // Create summary
                const visibleCount = analysisResults.filter(r => !r.wouldBeFiltered).length;
                const filteredCount = analysisResults.filter(r => r.wouldBeFiltered).length;
                const africaOnlyCount = analysisResults.filter(r => r.hasAfrica && !r.hasEurope && !r.hasMiddleEast && !r.isAmericas && !r.isAsiaPacific).length;

                const summaryDiv = document.createElement('div');
                summaryDiv.className = 'cable-info warning';
                summaryDiv.innerHTML = `
                    <h3>📊 Analysis Summary</h3>
                    <table>
                        <tr><th>Category</th><th>Count</th><th>Status</th></tr>
                        <tr><td>Total African-related cables found</td><td>${analysisResults.length}</td><td>-</td></tr>
                        <tr><td>Currently visible</td><td>${visibleCount}</td><td>${visibleCount > 0 ? '✅' : '❌'}</td></tr>
                        <tr><td>Currently filtered</td><td>${filteredCount}</td><td>${filteredCount === 0 ? '✅' : '⚠️'}</td></tr>
                        <tr><td>Africa-only cables</td><td>${africaOnlyCount}</td><td>-</td></tr>
                    </table>
                    
                    <h4>Key Findings:</h4>
                    <ul>
                        <li><strong>2Africa Status:</strong> ${analysisResults.find(r => r.cable.properties.id === '2africa') ? 
                            (analysisResults.find(r => r.cable.properties.id === '2africa').wouldBeFiltered ? 
                                '❌ Being filtered' : '✅ Should be visible') : '❓ Not found in data'}</li>
                        <li><strong>Geographic Filtering Impact:</strong> ${filteredCount > 0 ? 
                            `${filteredCount} African cables are being filtered by current logic` : 
                            'No African cables are being filtered'}</li>
                        <li><strong>Data Availability:</strong> ${analysisResults.length > 0 ? 
                            'African cable data is present in the dataset' : 
                            'No African cable data found'}</li>
                    </ul>
                    
                    <h4>Recommendations:</h4>
                    <ul>
                        <li>Check if 2Africa cable coordinates are causing filtering issues</li>
                        <li>Verify that African landing points are not being filtered</li>
                        <li>Consider African cable importance for global connectivity</li>
                        <li>Review geographic boundary definitions for accuracy</li>
                    </ul>
                `;
                resultsDiv.appendChild(summaryDiv);

                // Console logging
                console.log('=== AFRICAN CABLES DETAILED ANALYSIS ===');
                console.log(`Total found: ${analysisResults.length}`);
                console.log(`Visible: ${visibleCount}`);
                console.log(`Filtered: ${filteredCount}`);
                
                // Special focus on 2Africa
                const twoAfrica = analysisResults.find(r => r.cable.properties.id === '2africa');
                if (twoAfrica) {
                    console.log('\n=== 2AFRICA CABLE ANALYSIS ===');
                    console.log(`Status: ${twoAfrica.filterReason}`);
                    console.log(`Coordinates available: ${twoAfrica.coords ? 'Yes' : 'No'}`);
                    console.log(`Regional connectivity:`, {
                        Africa: twoAfrica.hasAfrica,
                        Europe: twoAfrica.hasEurope,
                        MiddleEast: twoAfrica.hasMiddleEast,
                        Americas: twoAfrica.isAmericas,
                        AsiaPacific: twoAfrica.isAsiaPacific
                    });
                } else {
                    console.log('\n❌ 2AFRICA CABLE NOT FOUND IN DATASET');
                }
            })
            .catch(error => {
                console.error('Error loading cable data:', error);
                document.getElementById('analysis-results').innerHTML = `
                    <div class="cable-info filtered">
                        <h3>❌ Analysis Failed</h3>
                        <p>Error loading cable data: ${error.message}</p>
                    </div>
                `;
            });

        // Add Europe region function for completeness
        function isInEuropeRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    
                    // Europe region boundaries:
                    // Longitude: -25°W to 45°E, Latitude: 35°N to 75°N
                    return (lng >= -25 && lng <= 45) && (lat >= 35 && lat <= 75);
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }
    </script>
</body>
</html>
