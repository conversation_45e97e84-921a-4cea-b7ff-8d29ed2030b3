<!DOCTYPE html>
<html>
<head>
    <title>Test Americas Cable Filter</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .pass { background-color: #d4edda; border-color: #c3e6cb; }
        .fail { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>Americas and Asia-Pacific Cable Filter Test</h1>
    <div id="test-results"></div>

    <script>
        // Function to check if coordinates are in Americas region (rough approximation)
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    // Americas longitude range (including Alaska and eastern Brazil)
                    // Expanded range to be more inclusive: -180° to -25°
                    return lng >= -180 && lng <= -25;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Function to check if coordinates are in Asia-Pacific region
        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    // Asia-Pacific region boundaries:
                    // Main Asia-Pacific: Longitude 60°E to 180°E, Latitude -50°S to 80°N
                    // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
                    const inMainAsiaPacific = (lng >= 60 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                    return inMainAsiaPacific || inPacificExtension;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Test cases
        const testCases = [
            {
                name: "US Cable (Americas - should be filtered)",
                coordinates: [[[-80.0, 25.0], [-70.0, 40.0]]],
                expectedAmericas: true,
                expectedAsiaPacific: false
            },
            {
                name: "European Cable (should not be filtered)",
                coordinates: [[[0.0, 50.0], [10.0, 55.0]]],
                expectedAmericas: false,
                expectedAsiaPacific: false
            },
            {
                name: "Japan Cable (Asia-Pacific - should be filtered)",
                coordinates: [[[140.0, 35.0], [150.0, 40.0]]],
                expectedAmericas: false,
                expectedAsiaPacific: true
            },
            {
                name: "Brazil Cable (Americas - should be filtered)",
                coordinates: [[[-45.0, -20.0], [-40.0, -15.0]]],
                expectedAmericas: true,
                expectedAsiaPacific: false
            },
            {
                name: "Australia Cable (Asia-Pacific - should be filtered)",
                coordinates: [[[150.0, -30.0], [155.0, -25.0]]],
                expectedAmericas: false,
                expectedAsiaPacific: true
            },
            {
                name: "Singapore Cable (Asia-Pacific - should be filtered)",
                coordinates: [[[103.8, 1.3], [105.0, 2.0]]],
                expectedAmericas: false,
                expectedAsiaPacific: true
            },
            {
                name: "Trans-Pacific Cable (both regions - should be filtered)",
                coordinates: [[[-120.0, 35.0], [140.0, 35.0]]],
                expectedAmericas: true,
                expectedAsiaPacific: true
            }
        ];

        // Run tests
        const resultsDiv = document.getElementById('test-results');
        let passCount = 0;
        let totalCount = testCases.length * 2; // Testing both Americas and Asia-Pacific

        testCases.forEach(testCase => {
            const americasResult = isInAmericasRegion(testCase.coordinates);
            const asiaPacificResult = isInAsiaPacificRegion(testCase.coordinates);
            const americasPassed = americasResult === testCase.expectedAmericas;
            const asiaPacificPassed = asiaPacificResult === testCase.expectedAsiaPacific;

            if (americasPassed) passCount++;
            if (asiaPacificPassed) passCount++;

            const div = document.createElement('div');
            div.className = `test-result ${(americasPassed && asiaPacificPassed) ? 'pass' : 'fail'}`;
            div.innerHTML = `
                <strong>${testCase.name}</strong><br>
                <strong>Americas Filter:</strong><br>
                Expected: ${testCase.expectedAmericas ? 'Filter' : 'Keep'} |
                Actual: ${americasResult ? 'Filter' : 'Keep'} |
                Status: ${americasPassed ? 'PASS' : 'FAIL'}<br>
                <strong>Asia-Pacific Filter:</strong><br>
                Expected: ${testCase.expectedAsiaPacific ? 'Filter' : 'Keep'} |
                Actual: ${asiaPacificResult ? 'Filter' : 'Keep'} |
                Status: ${asiaPacificPassed ? 'PASS' : 'FAIL'}
            `;
            resultsDiv.appendChild(div);
        });

        // Summary
        const summaryDiv = document.createElement('div');
        summaryDiv.className = 'test-result';
        summaryDiv.innerHTML = `
            <strong>Test Summary</strong><br>
            Passed: ${passCount}/${totalCount}<br>
            ${passCount === totalCount ? 'All tests passed!' : 'Some tests failed.'}
        `;
        resultsDiv.appendChild(summaryDiv);

        // Test with actual cable data
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log('Testing with actual cable data...');
                let americasCables = 0;
                let asiaPacificCables = 0;
                let bothRegionsCables = 0;
                let otherCables = 0;

                data.features.forEach(feature => {
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);

                        if (isAmericas && isAsiaPacific) {
                            bothRegionsCables++;
                            console.log(`Both regions cable: ${feature.properties.name}`);
                        } else if (isAmericas) {
                            americasCables++;
                            console.log(`Americas cable: ${feature.properties.name}`);
                        } else if (isAsiaPacific) {
                            asiaPacificCables++;
                            console.log(`Asia-Pacific cable: ${feature.properties.name}`);
                        } else {
                            otherCables++;
                        }
                    }
                });

                const actualTestDiv = document.createElement('div');
                actualTestDiv.className = 'test-result';
                actualTestDiv.innerHTML = `
                    <strong>Actual Cable Data Analysis</strong><br>
                    Total cables: ${data.features.length}<br>
                    Americas only cables (filtered): ${americasCables}<br>
                    Asia-Pacific only cables (filtered): ${asiaPacificCables}<br>
                    Both regions cables (filtered): ${bothRegionsCables}<br>
                    Total filtered: ${americasCables + asiaPacificCables + bothRegionsCables}<br>
                    Remaining cables (kept): ${otherCables}<br>
                    Check console for detailed list of filtered cables.
                `;
                resultsDiv.appendChild(actualTestDiv);
            })
            .catch(error => {
                console.error('Error loading cable data:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'test-result fail';
                errorDiv.innerHTML = `
                    <strong>Error loading cable data</strong><br>
                    ${error.message}
                `;
                resultsDiv.appendChild(errorDiv);
            });
    </script>
</body>
</html>
