<!DOCTYPE html>
<html>
<head>
    <title>Test Americas Cable Filter</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .pass { background-color: #d4edda; border-color: #c3e6cb; }
        .fail { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>Americas Cable Filter Test</h1>
    <div id="test-results"></div>

    <script>
        // Function to check if coordinates are in Americas region (rough approximation)
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    // Americas longitude range (including Alaska and eastern Brazil)
                    // Expanded range to be more inclusive: -180° to -25°
                    return lng >= -180 && lng <= -25;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Test cases
        const testCases = [
            {
                name: "US Cable (should be filtered)",
                coordinates: [[[-80.0, 25.0], [-70.0, 40.0]]],
                expected: true
            },
            {
                name: "European Cable (should not be filtered)",
                coordinates: [[[0.0, 50.0], [10.0, 55.0]]],
                expected: false
            },
            {
                name: "Pacific Cable (should not be filtered)",
                coordinates: [[[140.0, 35.0], [150.0, 40.0]]],
                expected: false
            },
            {
                name: "Brazil Cable (should be filtered)",
                coordinates: [[[-45.0, -20.0], [-40.0, -15.0]]],
                expected: true
            },
            {
                name: "Trans-Atlantic Cable (should be filtered - has Americas endpoint)",
                coordinates: [[[-70.0, 40.0], [0.0, 50.0]]],
                expected: true
            }
        ];

        // Run tests
        const resultsDiv = document.getElementById('test-results');
        let passCount = 0;
        let totalCount = testCases.length;

        testCases.forEach(testCase => {
            const result = isInAmericasRegion(testCase.coordinates);
            const passed = result === testCase.expected;
            
            if (passed) passCount++;

            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `
                <strong>${testCase.name}</strong><br>
                Expected: ${testCase.expected ? 'Filter (Americas)' : 'Keep (Non-Americas)'}<br>
                Actual: ${result ? 'Filter (Americas)' : 'Keep (Non-Americas)'}<br>
                Status: ${passed ? 'PASS' : 'FAIL'}
            `;
            resultsDiv.appendChild(div);
        });

        // Summary
        const summaryDiv = document.createElement('div');
        summaryDiv.className = 'test-result';
        summaryDiv.innerHTML = `
            <strong>Test Summary</strong><br>
            Passed: ${passCount}/${totalCount}<br>
            ${passCount === totalCount ? 'All tests passed!' : 'Some tests failed.'}
        `;
        resultsDiv.appendChild(summaryDiv);

        // Test with actual cable data
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log('Testing with actual cable data...');
                let americasCables = 0;
                let otherCables = 0;
                
                data.features.forEach(feature => {
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        if (isAmericas) {
                            americasCables++;
                            console.log(`Americas cable: ${feature.properties.name}`);
                        } else {
                            otherCables++;
                        }
                    }
                });

                const actualTestDiv = document.createElement('div');
                actualTestDiv.className = 'test-result';
                actualTestDiv.innerHTML = `
                    <strong>Actual Cable Data Analysis</strong><br>
                    Total cables: ${data.features.length}<br>
                    Americas cables (filtered): ${americasCables}<br>
                    Other cables (kept): ${otherCables}<br>
                    Check console for detailed list of filtered cables.
                `;
                resultsDiv.appendChild(actualTestDiv);
            })
            .catch(error => {
                console.error('Error loading cable data:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'test-result fail';
                errorDiv.innerHTML = `
                    <strong>Error loading cable data</strong><br>
                    ${error.message}
                `;
                resultsDiv.appendChild(errorDiv);
            });
    </script>
</body>
</html>
