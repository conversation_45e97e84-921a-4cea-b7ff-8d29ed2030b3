<!DOCTYPE html>
<html>
<head>
    <title>Middle East Connectivity Filter Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .pass { background-color: #d4edda; border-color: #c3e6cb; }
        .fail { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
    </style>
</head>
<body>
    <h1>Middle East Connectivity-Based Cable Filter Test</h1>
    <div id="test-results"></div>

    <script>
        // Copy all the region detection functions from main file
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    return lng >= -180 && lng <= -25;
                }
                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }

        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    const inMainAsiaPacific = (lng >= 60 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);
                    return inMainAsiaPacific || inPacificExtension;
                }
                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }

        function isInMiddleEastRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    return (lng >= 25 && lng <= 65) && (lat >= 10 && lat <= 45);
                }
                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }

        function isInAfricaRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    return (lng >= -20 && lng <= 55) && (lat >= -35 && lat <= 40);
                }
                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }

        function isInEuropeRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    return (lng >= -25 && lng <= 45) && (lat >= 35 && lat <= 75);
                }
                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }

        function shouldFilterMiddleEastCable(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;
            const hasMiddleEast = isInMiddleEastRegion(coordinates);
            if (!hasMiddleEast) return false;
            const hasAfrica = isInAfricaRegion(coordinates);
            const hasEurope = isInEuropeRegion(coordinates);
            if (hasAfrica || hasEurope) {
                return false; // Don't filter - keep this cable
            }
            return true; // Filter this cable
        }

        // Test cases for Middle East connectivity filtering
        const testCases = [
            {
                name: "Middle East to Europe Cable (should be KEPT)",
                coordinates: [[[35.0, 30.0], [10.0, 50.0]]], // Dubai to Germany
                expectedFilter: false
            },
            {
                name: "Middle East to Africa Cable (should be KEPT)",
                coordinates: [[[35.0, 30.0], [30.0, 20.0]]], // UAE to Egypt
                expectedFilter: false
            },
            {
                name: "Isolated Middle East Cable (should be FILTERED)",
                coordinates: [[[35.0, 30.0], [45.0, 25.0]]], // UAE to Saudi Arabia
                expectedFilter: true
            },
            {
                name: "Middle East to Asia Cable (should be FILTERED)",
                coordinates: [[[35.0, 30.0], [100.0, 10.0]]], // UAE to Singapore
                expectedFilter: true
            },
            {
                name: "Middle East to Americas Cable (should be FILTERED)",
                coordinates: [[[35.0, 30.0], [-80.0, 25.0]]], // UAE to US
                expectedFilter: true
            },
            {
                name: "Middle East-Europe-Africa Cable (should be KEPT)",
                coordinates: [[[35.0, 30.0], [10.0, 50.0], [20.0, 10.0]]], // UAE to Germany to Africa
                expectedFilter: false
            },
            {
                name: "Non-Middle East Cable (should not be affected)",
                coordinates: [[[0.0, 50.0], [10.0, 55.0]]], // European cable
                expectedFilter: false
            }
        ];

        // Run tests
        const resultsDiv = document.getElementById('test-results');
        let passCount = 0;
        let totalCount = testCases.length;

        testCases.forEach(testCase => {
            const result = shouldFilterMiddleEastCable(testCase.coordinates);
            const passed = result === testCase.expectedFilter;
            
            if (passed) passCount++;

            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `
                <strong>${testCase.name}</strong><br>
                Expected: ${testCase.expectedFilter ? 'FILTER (Hide)' : 'KEEP (Show)'}<br>
                Actual: ${result ? 'FILTER (Hide)' : 'KEEP (Show)'}<br>
                Status: ${passed ? 'PASS' : 'FAIL'}
            `;
            resultsDiv.appendChild(div);
        });

        // Summary
        const summaryDiv = document.createElement('div');
        summaryDiv.className = `test-result ${passCount === totalCount ? 'pass' : 'fail'}`;
        summaryDiv.innerHTML = `
            <strong>Test Summary</strong><br>
            Passed: ${passCount}/${totalCount}<br>
            ${passCount === totalCount ? 'All tests passed!' : 'Some tests failed.'}
        `;
        resultsDiv.appendChild(summaryDiv);

        // Test with actual cable data
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log('Testing Middle East filtering with actual cable data...');
                let americasCables = 0;
                let asiaPacificCables = 0;
                let middleEastFilteredCables = 0;
                let middleEastKeptCables = 0;
                let otherCables = 0;
                
                data.features.forEach(feature => {
                    if (feature.geometry && feature.geometry.coordinates) {
                        const coords = feature.geometry.coordinates;
                        const isAmericas = isInAmericasRegion(coords);
                        const isAsiaPacific = isInAsiaPacificRegion(coords);
                        const shouldFilterMiddleEast = shouldFilterMiddleEastCable(coords);
                        const hasMiddleEast = isInMiddleEastRegion(coords);
                        
                        if (isAmericas) {
                            americasCables++;
                        } else if (isAsiaPacific) {
                            asiaPacificCables++;
                        } else if (shouldFilterMiddleEast) {
                            middleEastFilteredCables++;
                            console.log(`Filtered Middle East cable: ${feature.properties.name}`);
                        } else if (hasMiddleEast) {
                            middleEastKeptCables++;
                            console.log(`Kept Middle East cable (has Africa/Europe connection): ${feature.properties.name}`);
                        } else {
                            otherCables++;
                        }
                    }
                });

                const actualTestDiv = document.createElement('div');
                actualTestDiv.className = 'test-result info';
                actualTestDiv.innerHTML = `
                    <strong>Actual Cable Data Analysis</strong><br>
                    Total cables: ${data.features.length}<br>
                    Americas cables (filtered): ${americasCables}<br>
                    Asia-Pacific cables (filtered): ${asiaPacificCables}<br>
                    Middle East cables (filtered): ${middleEastFilteredCables}<br>
                    Middle East cables (kept): ${middleEastKeptCables}<br>
                    Other cables (kept): ${otherCables}<br>
                    Total filtered: ${americasCables + asiaPacificCables + middleEastFilteredCables}<br>
                    Total kept: ${middleEastKeptCables + otherCables}<br>
                    Check console for detailed cable lists.
                `;
                resultsDiv.appendChild(actualTestDiv);
            })
            .catch(error => {
                console.error('Error loading cable data:', error);
            });
    </script>
</body>
</html>
