
<!DOCTYPE html>
<html>
<head>
    <title>Submarine Cable Map</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        
        body { margin: 0; padding: 0; }
        #map { 
            height: 100vh;
            width: 100%;
        }
        .info {
            padding: 6px 8px;
            font: 14px/16px Arial, Helvetica, sans-serif;
            background: white;
            background: rgba(255,255,255,0.8);
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize the map
        
        const map = L.map('map').setView([20, 0], 2);
        
        // Add OpenStreetMap tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // Create layer groups
        const cableLayer = L.layerGroup().addTo(map);
        const landingPointLayer = L.layerGroup().addTo(map);

        // Add info control
        const info = L.control();
        info.onAdd = function(map) {
            this._div = L.DomUtil.create('div', 'info');
            this.update();
            return this._div;
        };
        info.update = function(props) {
            this._div.innerHTML = '<h4>Submarine Cable Map</h4>' +
                '<div style="color: #d63384; font-size: 12px; margin-bottom: 8px;">⚠️ Americas, Asia-Pacific, and isolated Middle East cables are hidden</div>' +
                (props ?
                    '<b>' + props.name + '</b><br/>' +
                    (props.rfs ? 'Ready for Service: ' + props.rfs + '<br/>' : '') +
                    (props.length ? 'Length: ' + props.length + ' km<br/>' : '') +
                    (props.owners ? 'Owners: ' + props.owners + '<br/>' : '')
                    : 'Hover over a cable');
        };
        info.addTo(map);

        // Define Americas countries (North, Central, and South America)
        const americasCountries = new Set([
            // North America
            'United States', 'Canada', 'Mexico', 'Greenland',
            // Central America and Caribbean
            'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama',
            'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Bahamas', 'Barbados',
            'Trinidad and Tobago', 'Grenada', 'Saint Vincent and the Grenadines', 'Saint Lucia',
            'Dominica', 'Antigua and Barbuda', 'Saint Kitts and Nevis', 'Martinique', 'Guadeloupe',
            'Saint Barthélemy', 'Saint Martin', 'Sint Maarten', 'Anguilla', 'British Virgin Islands',
            'Virgin Islands (U.S.)', 'Virgin Islands (U.K.)', 'Cayman Islands', 'Turks and Caicos Islands',
            'Aruba', 'Curaçao', 'Bonaire, Sint Eustatius and Saba', 'Netherlands', 'French Guiana',
            // South America
            'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia',
            'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana'
        ]);

        // Define Asia-Pacific countries and territories
        const asiaPacificCountries = new Set([
            // East Asia
            'China', 'Japan', 'South Korea', 'North Korea', 'Taiwan', 'Hong Kong', 'Macau', 'Mongolia',
            // Southeast Asia
            'Singapore', 'Indonesia', 'Philippines', 'Malaysia', 'Vietnam', 'Thailand', 'Myanmar',
            'Cambodia', 'Laos', 'Brunei', 'Timor-Leste',
            // South Asia
            'India', 'Pakistan', 'Bangladesh', 'Sri Lanka', 'Nepal', 'Bhutan', 'Maldives', 'Afghanistan',
            // Oceania and Pacific Islands
            'Australia', 'New Zealand', 'Papua New Guinea', 'Fiji', 'Solomon Islands', 'Vanuatu',
            'New Caledonia', 'Samoa', 'Tonga', 'Kiribati', 'Tuvalu', 'Nauru', 'Palau', 'Marshall Islands',
            'Micronesia', 'Cook Islands', 'French Polynesia', 'Wallis and Futuna', 'American Samoa',
            'Guam', 'Northern Mariana Islands', 'Cocos (Keeling) Islands', 'Christmas Island'
        ]);

        // Define Middle East countries and territories
        const middleEastCountries = new Set([
            'Saudi Arabia', 'United Arab Emirates', 'UAE', 'Qatar', 'Kuwait', 'Bahrain', 'Oman', 'Yemen',
            'Iran', 'Iraq', 'Israel', 'Palestine', 'Jordan', 'Lebanon', 'Syria', 'Turkey', 'Cyprus'
        ]);

        // Define Africa countries (for connectivity analysis)
        const africaCountries = new Set([
            'Egypt', 'Libya', 'Tunisia', 'Algeria', 'Morocco', 'Sudan', 'South Sudan', 'Ethiopia', 'Eritrea',
            'Djibouti', 'Somalia', 'Kenya', 'Uganda', 'Tanzania', 'Rwanda', 'Burundi', 'Democratic Republic of the Congo',
            'Republic of the Congo', 'Central African Republic', 'Chad', 'Cameroon', 'Nigeria', 'Niger', 'Mali',
            'Burkina Faso', 'Ghana', 'Togo', 'Benin', 'Ivory Coast', 'Liberia', 'Sierra Leone', 'Guinea',
            'Guinea-Bissau', 'Senegal', 'Gambia', 'Mauritania', 'Cape Verde', 'Sao Tome and Principe',
            'Equatorial Guinea', 'Gabon', 'Angola', 'Zambia', 'Malawi', 'Mozambique', 'Zimbabwe', 'Botswana',
            'Namibia', 'South Africa', 'Lesotho', 'Swaziland', 'Madagascar', 'Mauritius', 'Seychelles', 'Comoros'
        ]);

        // Define Europe countries (for connectivity analysis)
        const europeCountries = new Set([
            'United Kingdom', 'Ireland', 'France', 'Spain', 'Portugal', 'Italy', 'Germany', 'Netherlands',
            'Belgium', 'Luxembourg', 'Switzerland', 'Austria', 'Denmark', 'Sweden', 'Norway', 'Finland',
            'Poland', 'Czech Republic', 'Slovakia', 'Hungary', 'Slovenia', 'Croatia', 'Bosnia and Herzegovina',
            'Serbia', 'Montenegro', 'Albania', 'North Macedonia', 'Bulgaria', 'Romania', 'Moldova',
            'Ukraine', 'Belarus', 'Lithuania', 'Latvia', 'Estonia', 'Russia', 'Greece', 'Malta', 'Iceland'
        ]);

        // Function to check if a cable has landing points in the Americas
        function isAmericasCable(cableId) {
            // We'll need to fetch individual cable data to check landing points
            // For now, we'll use a simpler approach based on cable coordinates
            return false; // Will be implemented below
        }

        // Function to check if coordinates are in Americas region (rough approximation)
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    // Americas longitude range (including Alaska and eastern Brazil)
                    // Expanded range to be more inclusive: -180° to -25°
                    return lng >= -180 && lng <= -25;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Function to check if coordinates are in Asia-Pacific region
        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    // Asia-Pacific region boundaries:
                    // Main Asia-Pacific: Longitude 60°E to 180°E, Latitude -50°S to 80°N
                    // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
                    const inMainAsiaPacific = (lng >= 60 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                    return inMainAsiaPacific || inPacificExtension;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Function to check if coordinates are in Middle East region
        function isInMiddleEastRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    // Middle East region boundaries:
                    // Longitude: 25°E to 65°E, Latitude: 10°N to 45°N
                    return (lng >= 25 && lng <= 65) && (lat >= 10 && lat <= 45);
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Function to check if coordinates are in Africa region
        function isInAfricaRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    // Africa region boundaries (rough approximation):
                    // Longitude: -20°W to 55°E, Latitude: -35°S to 40°N
                    return (lng >= -20 && lng <= 55) && (lat >= -35 && lat <= 40);
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Function to check if coordinates are in Europe region
        function isInEuropeRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    // Europe region boundaries (rough approximation):
                    // Longitude: -25°W to 45°E, Latitude: 35°N to 75°N
                    return (lng >= -25 && lng <= 45) && (lat >= 35 && lat <= 75);
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Function to determine if a Middle East cable should be filtered
        function shouldFilterMiddleEastCable(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            const hasMiddleEast = isInMiddleEastRegion(coordinates);
            if (!hasMiddleEast) return false; // Not a Middle East cable

            const hasAfrica = isInAfricaRegion(coordinates);
            const hasEurope = isInEuropeRegion(coordinates);
            const hasAmericas = isInAmericasRegion(coordinates);
            const hasAsiaPacific = isInAsiaPacificRegion(coordinates);

            // Keep cables that connect Middle East to Africa or Europe
            if (hasAfrica || hasEurope) {
                return false; // Don't filter - keep this cable
            }

            // Filter cables that are:
            // 1. Entirely within Middle East
            // 2. Connect Middle East only to Americas or Asia-Pacific (which are already filtered)
            return true; // Filter this cable
        }

        // Fetch and display cable routes
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total cables before filtering: ${data.features.length}`);

                // Filter out cables in Americas, Asia-Pacific, and isolated Middle East regions
                const filteredFeatures = data.features.filter(feature => {
                    // Check if cable coordinates are in filtered regions
                    if (feature.geometry && feature.geometry.coordinates) {
                        const coords = feature.geometry.coordinates;
                        const isAmericas = isInAmericasRegion(coords);
                        const isAsiaPacific = isInAsiaPacificRegion(coords);
                        const shouldFilterMiddleEast = shouldFilterMiddleEastCable(coords);

                        if (isAmericas || isAsiaPacific || shouldFilterMiddleEast) {
                            // Filter this cable
                            return false;
                        }
                        return true; // Keep cable if it's not in filtered regions
                    }
                    return true; // Keep cable if we can't determine location
                });

                console.log(`Total cables after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                L.geoJSON(filteredData, {
                    style: function(feature) {
                        return {
                            color: feature.properties.is_planned ? '#FF9900' : '#0066CC',
                            weight: 2,
                            opacity: 0.8
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        layer.on({
                            mouseover: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                });
                                info.update(feature.properties);
                            },
                            mouseout: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2,
                                    opacity: 0.8
                                });
                                info.update();
                            },
                            click: function(e) {
                                map.fitBounds(e.target.getBounds());
                            }
                        });

                        let popupContent = '<b>' + feature.properties.name + '</b><br/>';
                        if (feature.properties.rfs) {
                            popupContent += 'Ready for Service: ' + feature.properties.rfs + '<br/>';
                        }
                        if (feature.properties.length) {
                            popupContent += 'Length: ' + feature.properties.length + ' km<br/>';
                        }
                        if (feature.properties.owners) {
                            popupContent += 'Owners: ' + feature.properties.owners;
                        }
                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        // Function to check if landing point coordinates are in Americas region
        function isLandingPointInAmericas(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            // Americas longitude range (including Alaska and eastern Brazil)
            // Expanded range to be more inclusive: -180° to -25°
            return lng >= -180 && lng <= -25;
        }

        // Function to check if landing point coordinates are in Asia-Pacific region
        function isLandingPointInAsiaPacific(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            // Asia-Pacific region boundaries:
            // Main Asia-Pacific: Longitude 60°E to 180°E, Latitude -50°S to 80°N
            // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
            const inMainAsiaPacific = (lng >= 60 && lng <= 180) && (lat >= -50 && lat <= 80);
            const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

            return inMainAsiaPacific || inPacificExtension;
        }

        // Function to check if landing point coordinates are in Middle East region
        function isLandingPointInMiddleEast(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            // Middle East region boundaries:
            // Longitude: 25°E to 65°E, Latitude: 10°N to 45°N
            return (lng >= 25 && lng <= 65) && (lat >= 10 && lat <= 45);
        }

        // Function to check if landing point coordinates are in Africa region
        function isLandingPointInAfrica(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            // Africa region boundaries:
            // Longitude: -20°W to 55°E, Latitude: -35°S to 40°N
            return (lng >= -20 && lng <= 55) && (lat >= -35 && lat <= 40);
        }

        // Function to check if landing point coordinates are in Europe region
        function isLandingPointInEurope(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            // Europe region boundaries:
            // Longitude: -25°W to 45°E, Latitude: 35°N to 75°N
            return (lng >= -25 && lng <= 45) && (lat >= 35 && lat <= 75);
        }

        // Function to determine if a Middle East landing point should be filtered
        // This is a simplified version for landing points - we'll filter Middle East landing points
        // that are not connected to Africa/Europe cables (but this requires cable analysis)
        function shouldFilterMiddleEastLandingPoint(coordinates) {
            // For now, we'll use a conservative approach and only filter landing points
            // that are clearly isolated Middle East points
            // In a full implementation, this would require analyzing which cables use this landing point
            return isLandingPointInMiddleEast(coordinates);
        }

        // Fetch and display landing points
        fetch('../landing-point/landing-point-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total landing points before filtering: ${data.features.length}`);

                // Filter out landing points in Americas, Asia-Pacific, and isolated Middle East regions
                const filteredFeatures = data.features.filter(feature => {
                    if (feature.geometry && feature.geometry.coordinates) {
                        const coords = feature.geometry.coordinates;
                        const isAmericas = isLandingPointInAmericas(coords);
                        const isAsiaPacific = isLandingPointInAsiaPacific(coords);
                        const shouldFilterMiddleEast = shouldFilterMiddleEastLandingPoint(coords);

                        if (isAmericas) {
                            console.log(`Filtering out Americas landing point: ${feature.properties.name}`);
                            return false;
                        }
                        if (isAsiaPacific) {
                            console.log(`Filtering out Asia-Pacific landing point: ${feature.properties.name}`);
                            return false;
                        }
                        if (shouldFilterMiddleEast) {
                            console.log(`Filtering out isolated Middle East landing point: ${feature.properties.name}`);
                            return false;
                        }
                        return true; // Keep landing point if it's not in filtered regions
                    }
                    return true; // Keep landing point if we can't determine location
                });

                console.log(`Total landing points after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                L.geoJSON(filteredData, {
                    pointToLayer: (feature, latlng) => {
                        return L.circleMarker(latlng, {
                            radius: 5,
                            fillColor: '#FF0000',
                            color: '#000',
                            weight: 1,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties.name) {
                            layer.bindPopup(`<b>${feature.properties.name}</b><br>
                                ${feature.properties.country || ''}`);
                        }
                    }
                }).addTo(landingPointLayer);
            });

        // Add layer control
        const baseMaps = {
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'),
            "Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}')
        };

        const overlayMaps = {
            "Submarine Cables": cableLayer,
            "Landing Points": landingPointLayer
        };

        L.control.layers(baseMaps, overlayMaps).addTo(map);

        // Add scale control
        L.control.scale().addTo(map);
    </script>
</body>
</html>
