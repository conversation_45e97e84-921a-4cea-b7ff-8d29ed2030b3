<!DOCTYPE html>
<html>
<head>
    <title>Submarine Cable Map</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        
        body { margin: 0; padding: 0; }
        #map { 
            height: 100vh;
            width: 100%;
        }
        .info {
            padding: 6px 8px;
            font: 14px/16px Arial, Helvetica, sans-serif;
            background: white;
            background: rgba(255,255,255,0.8);
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize the map
        
        const map = L.map('map').setView([20, 0], 2);
        
        // Add OpenStreetMap tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // Create layer groups
        const cableLayer = L.layerGroup().addTo(map);
        const landingPointLayer = L.layerGroup().addTo(map);

        // Add info control
        const info = L.control();
        info.onAdd = function(map) {
            this._div = L.DomUtil.create('div', 'info');
            this.update();
            return this._div;
        };
        info.update = function(props) {
            this._div.innerHTML = '<h4>Submarine Cable Map</h4>' +
                '<div style="color: #d63384; font-size: 12px; margin-bottom: 8px;">⚠️ Americas region cables are aviable</div>' +
                (props ?
                    '<b>' + props.name + '</b><br/>' +
                    (props.rfs ? 'Ready for Service: ' + props.rfs + '<br/>' : '') +
                    (props.length ? 'Length: ' + props.length + ' km<br/>' : '') +
                    (props.owners ? 'Owners: ' + props.owners + '<br/>' : '')
                    : 'Hover over a cable');
        };
        info.addTo(map);

        // Define Americas countries (North, Central, and South America)
        const americasCountries = new Set([
            // North America
            'United States', 'Canada', 'Mexico', 'Greenland',
            // Central America and Caribbean
            'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama',
            'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Bahamas', 'Barbados',
            'Trinidad and Tobago', 'Grenada', 'Saint Vincent and the Grenadines', 'Saint Lucia',
            'Dominica', 'Antigua and Barbuda', 'Saint Kitts and Nevis', 'Martinique', 'Guadeloupe',
            'Saint Barthélemy', 'Saint Martin', 'Sint Maarten', 'Anguilla', 'British Virgin Islands',
            'Virgin Islands (U.S.)', 'Virgin Islands (U.K.)', 'Cayman Islands', 'Turks and Caicos Islands',
            'Aruba', 'Curaçao', 'Bonaire, Sint Eustatius and Saba', 'Netherlands', 'French Guiana',
            // South America
            'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia',
            'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana'
        ]);

        // Function to check if a cable has landing points in the Americas
        function isAmericasCable(cableId) {
            // We'll need to fetch individual cable data to check landing points
            // For now, we'll use a simpler approach based on cable coordinates
            return false; // Will be implemented below
        }

        // Function to check if coordinates are in Americas region (rough approximation)
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    // Americas longitude range (including Alaska and eastern Brazil)
                    // Expanded range to be more inclusive: -180° to -25°
                    return lng >= -180 && lng <= -25;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Fetch and display cable routes
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total cables before filtering: ${data.features.length}`);

                // Filter out cables in Americas region
                const filteredFeatures = data.features.filter(feature => {
                    // Check if cable coordinates are in Americas region
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        if (isAmericas) {
                            console.log(`Filtering out Americas cable: ${feature.properties.name}`);
                        }
                        return !isAmericas;
                    }
                    return true; // Keep cable if we can't determine location
                });

                console.log(`Total cables after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                L.geoJSON(filteredData, {
                    style: function(feature) {
                        return {
                            color: feature.properties.is_planned ? '#FF9900' : '#0066CC',
                            weight: 2,
                            opacity: 0.8
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        layer.on({
                            mouseover: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                });
                                info.update(feature.properties);
                            },
                            mouseout: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2,
                                    opacity: 0.8
                                });
                                info.update();
                            },
                            click: function(e) {
                                map.fitBounds(e.target.getBounds());
                            }
                        });

                        let popupContent = '<b>' + feature.properties.name + '</b><br/>';
                        if (feature.properties.rfs) {
                            popupContent += 'Ready for Service: ' + feature.properties.rfs + '<br/>';
                        }
                        if (feature.properties.length) {
                            popupContent += 'Length: ' + feature.properties.length + ' km<br/>';
                        }
                        if (feature.properties.owners) {
                            popupContent += 'Owners: ' + feature.properties.owners;
                        }
                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        // Function to check if landing point coordinates are in Americas region
        function isLandingPointInAmericas(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            // Americas longitude range (including Alaska and eastern Brazil)
            // Expanded range to be more inclusive: -180° to -25°
            return lng >= -180 && lng <= -25;
        }

        // Fetch and display landing points
        fetch('../landing-point/landing-point-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total landing points before filtering: ${data.features.length}`);

                // Filter out landing points in Americas region
                const filteredFeatures = data.features.filter(feature => {
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isLandingPointInAmericas(feature.geometry.coordinates);
                        if (isAmericas) {
                            console.log(`Filtering out Americas landing point: ${feature.properties.name}`);
                        }
                        return !isAmericas;
                    }
                    return true; // Keep landing point if we can't determine location
                });

                console.log(`Total landing points after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                L.geoJSON(filteredData, {
                    pointToLayer: (feature, latlng) => {
                        return L.circleMarker(latlng, {
                            radius: 5,
                            fillColor: '#FF0000',
                            color: '#000',
                            weight: 1,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties.name) {
                            layer.bindPopup(`<b>${feature.properties.name}</b><br>
                                ${feature.properties.country || ''}`);
                        }
                    }
                }).addTo(landingPointLayer);
            });

        // Add layer control
        const baseMaps = {
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'),
            "Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}')
        };

        const overlayMaps = {
            "Submarine Cables": cableLayer,
            "Landing Points": landingPointLayer
        };

        L.control.layers(baseMaps, overlayMaps).addTo(map);

        // Add scale control
        L.control.scale().addTo(map);
    </script>
</body>
</html>
